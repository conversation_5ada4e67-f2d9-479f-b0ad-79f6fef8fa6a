import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';

class CompleteYourProfileScreen extends StatefulWidget {
  const CompleteYourProfileScreen({super.key});

  @override
  State<CompleteYourProfileScreen> createState() =>
      _CompleteYourProfileScreenState();
}

class _CompleteYourProfileScreenState extends State<CompleteYourProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final countryPicker = const FlCountryCodePicker(
    title: SizedBox.shrink(),
  );
  TextEditingController genderC = TextEditingController();
  TextEditingController occupationC = TextEditingController();
  TextEditingController addressC = TextEditingController();
  TextEditingController dobC = TextEditingController();

  FocusNode genderF = FocusNode();
  FocusNode occupationF = FocusNode();
  FocusNode addressF = FocusNode();
  FocusNode dobF = FocusNode();

  DateTime? selectedDate;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    genderC.dispose();
    occupationC.dispose();
    addressC.dispose();
    dobC.dispose();

    genderF.dispose();
    occupationF.dispose();
    addressF.dispose();
    dobF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          children: [
            const YBox(20),
            const CustomSubHeader(
              title: 'Complete Your Profile',
              subtitle: "Please enter the information below",
            ),
            const YBox(40),
            CustomTextField(
              controller: genderC,
              focusNode: genderF,
              labelText: 'Gender',
              showLabelHeader: true,
              isReadOnly: true,
              isRequired: true,
              suffixIcon: const Icon(
                Iconsax.arrow_down_1,
              ),
              onChanged: (v) => setState(() {}),
              onTap: () async {
                final r = await ModalWrapper.bottomSheet(
                  context: context,
                  widget: const SelectionModal(
                      selections: ['Male', 'Female', 'Other']),
                );

                if (r is String && context.mounted) {
                  genderC.text = r;
                  setState(() {});
                }
              },
            ),
            const YBox(20),
            CustomTextField(
              controller: occupationC,
              focusNode: occupationF,
              labelText: 'Occupation',
              showLabelHeader: true,
              isRequired: true,
              validationRules: ValidationRules.requiredEmail(),
              onChanged: (v) => setState(() {}),
            ),
            const YBox(20),
            CustomTextField(
              controller: addressC,
              focusNode: addressF,
              labelText: 'Address',
              showLabelHeader: true,
              onChanged: (v) => setState(() {}),
            ),
            const YBox(20),
            CustomTextField(
              controller: dobC,
              focusNode: dobF,
              labelText: 'Date of birth',
              showLabelHeader: true,
              isReadOnly: true,
              suffixIcon: const Icon(
                Iconsax.calendar_1,
              ),
              onChanged: (v) => setState(() {}),
              onTap: () {
                CustomCupertinoDatePicker(
                  context: context,
                  // Set initial date to 25 years ago (reasonable default for adults)
                  initialDateTime:
                      DateTime.now().subtract(const Duration(days: 365 * 25)),
                  // Allow dates from 100 years ago to 18 years ago (minimum age requirement)
                  minimumDate:
                      DateTime.now().subtract(const Duration(days: 365 * 100)),
                  maximumDate:
                      DateTime.now().subtract(const Duration(days: 365 * 18)),
                  onDateTimeChanged: (dateTime) {
                    dobC.text = AppUtils.dayWithSuffixMonthAndYear(dateTime);
                    setState(() {});
                  },
                ).show();
              },
            ),
            const YBox(60),
            CustomBtn.solid(
              text: 'Continue',
              onTap: () {
                if (!_formKey.currentState!.validate()) return;
                // Navigator.pushNamed(
                //     context, RoutePath.verifyYourIdentityScreen);
              },
            ),
          ],
        ),
      ),
    );
  }
}
